using System;
using System.IO;
using System.Collections.Generic;

class Program
{
    static void Main()
    {
        string inputFile = @"S:\Documents\NinjaTrader 8\ES 09-25.Last.csv";
        string outputFile = @"S:\Documents\NinjaTrader 8\ES 09-25.Last_NoDuplicates.csv";
        
        HashSet<string> seenTimestamps = new HashSet<string>();
        
        using (StreamReader reader = new StreamReader(inputFile))
        using (StreamWriter writer = new StreamWriter(outputFile))
        {
            string line;
            while ((line = reader.ReadLine()) != null)
            {
                // Extract timestamp (first 21 characters: yyyyMMdd HHmmss fffffff)
                if (line.Length >= 21)
                {
                    string timestamp = line.Substring(0, 21);
                    
                    if (!seenTimestamps.Contains(timestamp))
                    {
                        seenTimestamps.Add(timestamp);
                        writer.WriteLine(line);
                    }
                }
            }
        }
        
        Console.WriteLine($"Duplicate removal complete. Output saved to: {outputFile}");
    }
}