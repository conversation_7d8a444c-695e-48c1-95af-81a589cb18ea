#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
    public class AdvancedOrderManagement : Strategy
    {
        #region Strategy Parameters
       
        // General Strategy Controls
        [NinjaScriptProperty]
        [Display(Name = "Use Chase Strategy", Description = "Enable chase functionality", Order = 1, GroupName = "Strategy Controls")]
        public bool UseChase { get; set; } = true;
       
        [NinjaScriptProperty]
        [Display(Name = "Use Escape Strategy", Description = "Enable escape functionality", Order = 2, GroupName = "Strategy Controls")]
        public bool UseEscape { get; set; } = true;
       
        [NinjaScriptProperty]
        [Display(Name = "Use Execute Strategy", Description = "Enable execute functionality", Order = 3, GroupName = "Strategy Controls")]
        public bool UseExecute { get; set; } = true;
       
        // Chase Parameters
        [NinjaScriptProperty]
        [Range(1, 10)]
        [Display(Name = "Chase Max Distance (Ticks)", Description = "Maximum distance to chase the market", Order = 1, GroupName = "Chase Settings")]
        public int ChaseMaxDistance { get; set; } = 2;
       
        [NinjaScriptProperty]
        [Range(100, 2000)]
        [Display(Name = "Chase Update Frequency (ms)", Description = "How often to check chase conditions", Order = 2, GroupName = "Chase Settings")]
        public int ChaseUpdateFrequency { get; set; } = 500;
       
        // Escape Parameters
        [NinjaScriptProperty]
        [Range(1, 10)]
        [Display(Name = "Escape Distance (Ticks)", Description = "Distance from best price to trigger escape check", Order = 1, GroupName = "Escape Settings")]
        public int EscapeDistance { get; set; } = 3;
       
        [NinjaScriptProperty]
        [Range(1, 5)]
        [Display(Name = "Escape Depth Levels", Description = "Number of price levels to analyze", Order = 2, GroupName = "Escape Settings")]
        public int EscapeDepthLevels { get; set; } = 2;
       
        [NinjaScriptProperty]
        [Range(0.1, 0.9)]
        [Display(Name = "Escape Imbalance Threshold", Description = "Imbalance threshold to trigger escape (0.6 = 60%)", Order = 3, GroupName = "Escape Settings")]
        public double EscapeImbalanceThreshold { get; set; } = 0.6;
       
        // Execute Parameters
        [NinjaScriptProperty]
        [Range(1, 5)]
        [Display(Name = "Execute Depth Levels", Description = "Number of price levels to analyze for execution", Order = 1, GroupName = "Execute Settings")]
        public int ExecuteDepthLevels { get; set; } = 1;
       
        [NinjaScriptProperty]
        [Range(1.5, 5.0)]
        [Display(Name = "Execute Imbalance Threshold", Description = "Imbalance threshold to trigger execution (2.0 = 200%)", Order = 2, GroupName = "Execute Settings")]
        public double ExecuteImbalanceThreshold { get; set; } = 2.0;
       
        [NinjaScriptProperty]
        [Range(1, 5)]
        [Display(Name = "Execute Ticks Ahead", Description = "Ticks to move ahead of best price", Order = 3, GroupName = "Execute Settings")]
        public int ExecuteTicksAhead { get; set; } = 2;
       
        // Entry Logic Parameters
        [NinjaScriptProperty]
        [Range(1, 100)]
        [Display(Name = "Entry Signal Strength", Description = "Signal strength threshold for entry", Order = 1, GroupName = "Entry Logic")]
        public int EntrySignalStrength { get; set; } = 50;
       
        [NinjaScriptProperty]
        [Range(1, 50)]
        [Display(Name = "Bars Lookback", Description = "Bars to lookback for entry signals", Order = 2, GroupName = "Entry Logic")]
        public int BarsLookback { get; set; } = 10;
       
        // Risk Management
        [NinjaScriptProperty]
        [Range(1, 100)]
        [Display(Name = "Max Modifications Per Minute", Description = "Maximum order modifications per minute", Order = 1, GroupName = "Risk Management")]
        public int MaxModificationsPerMinute { get; set; } = 20;
       
        [NinjaScriptProperty]
        [Range(1, 10)]
        [Display(Name = "Order Quantity", Description = "Order size", Order = 2, GroupName = "Risk Management")]
        public int OrderQuantity { get; set; } = 1;
       
        // Exit Strategy Settings
        [NinjaScriptProperty]
        [Range(1, 50)]
        [Display(Name = "Trailing Stop Ticks", Description = "Ticks away from previous bar high/low for trailing stop", Order = 1, GroupName = "Exit Settings")]
        public int TrailingStopTicks { get; set; } = 1;
       
        [NinjaScriptProperty]
        [Range(0, 100)]
        [Display(Name = "Min Trail Distance", Description = "Minimum profit before trailing starts (0 = immediate)", Order = 2, GroupName = "Exit Settings")]
        public int MinTrailDistance { get; set; } = 0;
       
        [NinjaScriptProperty]
        [Range(5, 200)]
        [Display(Name = "Emergency Stop Ticks", Description = "Maximum loss from entry price", Order = 3, GroupName = "Exit Settings")]
        public int EmergencyStopTicks { get; set; } = 50;
       
        [NinjaScriptProperty]
        [Display(Name = "Use Trend Reversal Exit", Description = "Exit on reversed entry conditions", Order = 4, GroupName = "Exit Settings")]
        public bool UseTrendReversalExit { get; set; } = true;
       
        [NinjaScriptProperty]
        [Display(Name = "Use Trailing Stop", Description = "Enable trailing stop loss", Order = 5, GroupName = "Exit Settings")]
        public bool UseTrailingStop { get; set; } = true;
       
        #endregion
       
        #region Private Variables
       
        // Strategy State
        private enum StrategyState { Flat, WorkingOrder, InPosition }
        private StrategyState currentState = StrategyState.Flat;
       
        // Market data
        private double currentBid;
        private double currentAsk;
        private long currentBidSize;
        private long currentAskSize;
        private DateTime lastUpdate;
       
        // Order management
        private Order workingOrder;
        private string atmStrategyId = string.Empty;
        private string orderId = string.Empty;
        private bool isLongEntry = true;
       
        // Modification tracking
        private Dictionary<string, DateTime> orderModifications;
        private int modificationsThisMinute;
        private DateTime lastMinuteReset;
       
        // Strategy timing
        private DateTime lastChaseUpdate;
        private DateTime lastEntrySignalCheck;
       
        // Market depth data
        private Dictionary<double, long> bidDepth;
        private Dictionary<double, long> askDepth;
       
        // Performance tracking
        private int totalModifications;
        private int chaseModifications;
        private int escapeModifications;
        private int executeModifications;
        private int entriesAttempted;
        private int entriesFilled;
       
        #endregion
       
        #region Strategy Lifecycle
       
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = @"Advanced Order Management Strategy combining Chase, Escape, and Execute logic with ATM integration";
                Name = "AdvancedOrderManagement";
                Calculate = Calculate.OnEachTick;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                IsInstantiatedOnEachOptimizationIteration = false;
               
                // Enable unmanaged orders
                IsUnmanaged = true;
            }
            else if (State == State.DataLoaded)
            {
                // Initialize collections
                orderModifications = new Dictionary<string, DateTime>();
                bidDepth = new Dictionary<double, long>();
                askDepth = new Dictionary<double, long>();
               
                // Reset counters
                modificationsThisMinute = 0;
                lastMinuteReset = DateTime.Now;
                lastChaseUpdate = DateTime.MinValue;
                lastEntrySignalCheck = DateTime.MinValue;
               
                // Set initial state
                currentState = StrategyState.Flat;
               
                Print("Advanced Order Management Strategy initialized - Hybrid Mode");
            }
            else if (State == State.Terminated)
            {
                PrintPerformanceStats();
            }
        }
       
        #endregion
       
        #region Market Data Handlers
       
        protected override void OnMarketData(MarketDataEventArgs marketDataUpdate)
        {
            if (marketDataUpdate.MarketDataType == MarketDataType.Bid)
            {
                currentBid = marketDataUpdate.Price;
                currentBidSize = marketDataUpdate.Volume;
            }
            else if (marketDataUpdate.MarketDataType == MarketDataType.Ask)
            {
                currentAsk = marketDataUpdate.Price;
                currentAskSize = marketDataUpdate.Volume;
            }
           
            lastUpdate = DateTime.Now;
           
            // Process strategies based on current state
            ProcessStrategies();
        }
       
        protected override void OnMarketDepth(MarketDepthEventArgs marketDepthUpdate)
        {
            try
            {
                // Update depth tracking
                if (marketDepthUpdate.MarketDataType == MarketDataType.Bid)
                {
                    UpdateDepthData(bidDepth, marketDepthUpdate);
                }
                else if (marketDepthUpdate.MarketDataType == MarketDataType.Ask)
                {
                    UpdateDepthData(askDepth, marketDepthUpdate);
                }
               
                // Process strategies after depth update
                if (currentState == StrategyState.WorkingOrder)
                {
                    ProcessWorkingOrderStrategies();
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnMarketDepth: {ex.Message}");
            }
        }
       
        private void UpdateDepthData(Dictionary<double, long> depthDict, MarketDepthEventArgs e)
        {
            if (e.Operation == Operation.Update || e.Operation == Operation.Add)
            {
                depthDict[e.Price] = e.Volume;
            }
            else if (e.Operation == Operation.Remove)
            {
                depthDict.Remove(e.Price);
            }
        }
       
        #endregion
       
        #region Order Management
       
        protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string comment)
        {
            // Handle working order updates
            if (order == workingOrder)
            {
                HandleWorkingOrderUpdate(order, orderState, filled, error);
            }
           
            // Handle ATM order updates
            if (orderId.Length > 0 && order.Name == orderId)
            {
                HandleATMOrderUpdate(order, orderState, error);
            }
        }
       
        private void HandleWorkingOrderUpdate(Order order, OrderState orderState, int filled, ErrorCode error)
        {
            if (orderState == OrderState.Filled)
            {
                // Order filled - transition to ATM management
                entriesFilled++;
                TransitionToATMManagement(order);
            }
            else if (orderState == OrderState.Cancelled || orderState == OrderState.Rejected)
            {
                // Order cancelled/rejected - return to flat
                workingOrder = null;
                currentState = StrategyState.Flat;
               
                if (error != ErrorCode.NoError)
                {
                    Print($"Working order error: {error}");
                }
            }
            else if (orderState == OrderState.PartFilled && filled > 0)
            {
                // Handle partial fills - could transition to ATM for filled portion
                Print($"Partial fill: {filled} of {order.Quantity}");
            }
        }
       
        private void HandleATMOrderUpdate(Order order, OrderState orderState, ErrorCode error)
        {
            if (orderState == OrderState.Filled && (order.Name.Contains("Stop") || order.Name.Contains("Target")))
            {
                // ATM exit filled - return to flat
                currentState = StrategyState.Flat;
                atmStrategyId = string.Empty;
                orderId = string.Empty;
                Print("ATM exit filled - returning to flat");
            }
        }
       
        private void TransitionToATMManagement(Order filledOrder)
        {
            try
            {
                currentState = StrategyState.InPosition;
               
                // Create ATM strategy for position management
                atmStrategyId = GetAtmStrategyUniqueId();
                orderId = filledOrder.Name;
               
                // This would attach ATM to the filled order
                // Note: In practice, you'd need to configure your ATM template
                Print($"Position filled - ATM taking over position management. ATM ID: {atmStrategyId}");
               
                // Clear working order reference
                workingOrder = null;
            }
            catch (Exception ex)
            {
                Print($"Error transitioning to ATM: {ex.Message}");
            }
        }
       
        #endregion
       
        #region Strategy Processing
       
        private void ProcessStrategies()
        {
            if (!IsValidMarketData())
                return;
               
            UpdateRiskManagement();
           
            if (currentState == StrategyState.Flat)
            {
                CheckForEntrySignals();
            }
            else if (currentState == StrategyState.WorkingOrder)
            {
                ProcessWorkingOrderStrategies();
            }
            // InPosition state is managed by ATM
        }
       
        private void ProcessWorkingOrderStrategies()
        {
            if (workingOrder == null || workingOrder.OrderState != OrderState.Working)
                return;
           
            // Priority: Escape > Execute > Chase
            if (UseEscape && CheckEscapeConditions())
            {
                return; // Escape takes highest priority
            }
           
            if (UseExecute && CheckExecuteConditions())
            {
                return; // Execute takes second priority
            }
           
            if (UseChase && ShouldUpdateChase())
            {
                CheckChaseConditions();
            }
        }
       
        private bool IsValidMarketData()
        {
            return currentBid > 0 && currentAsk > 0 && currentAsk > currentBid;
        }
       
        private bool ShouldUpdateChase()
        {
            return DateTime.Now.Subtract(lastChaseUpdate).TotalMilliseconds >= ChaseUpdateFrequency;
        }
       
        #endregion
       
        #region Entry Logic
       
        private void CheckForEntrySignals()
        {
            // Only check entry signals periodically to avoid excessive processing
            if (DateTime.Now.Subtract(lastEntrySignalCheck).TotalMilliseconds < 1000)
                return;
               
            lastEntrySignalCheck = DateTime.Now;
           
            // Entry signal logic based on your strategy ideas
            bool longSignal = EvaluateLongEntrySignal();
            bool shortSignal = EvaluateShortEntrySignal();
           
            if (longSignal && !shortSignal)
            {
                PlaceEntryOrder(OrderAction.Buy);
            }
            else if (shortSignal && !longSignal)
            {
                PlaceEntryOrder(OrderAction.Sell);
            }
        }
       
        private bool EvaluateLongEntrySignal()
        {
            // Example entry logic - customize based on your specific requirements
            // This is a simple momentum-based entry
           
            if (CurrentBar < BarsLookback)
                return false;
               
            // Check if price is moving up with volume
            bool priceMovingUp = Close[0] > Close[1] && Close[1] > Close[2];
            bool volumeIncreasing = Volume[0] > Volume[1];
           
            // Check for favorable market depth imbalance
            double totalBidSize = GetTotalSizeAtLevels(MarketDataType.Bid, ExecuteDepthLevels);
            double totalAskSize = GetTotalSizeAtLevels(MarketDataType.Ask, ExecuteDepthLevels);
            bool favorableImbalance = totalAskSize > 0 && (totalBidSize / totalAskSize) > 1.2;
           
            return priceMovingUp && volumeIncreasing && favorableImbalance;
        }
       
        private bool EvaluateShortEntrySignal()
        {
            // Example entry logic for short
            if (CurrentBar < BarsLookback)
                return false;
               
            // Check if price is moving down with volume
            bool priceMovingDown = Close[0] < Close[1] && Close[1] < Close[2];
            bool volumeIncreasing = Volume[0] > Volume[1];
           
            // Check for favorable market depth imbalance
            double totalBidSize = GetTotalSizeAtLevels(MarketDataType.Bid, ExecuteDepthLevels);
            double totalAskSize = GetTotalSizeAtLevels(MarketDataType.Ask, ExecuteDepthLevels);
            bool favorableImbalance = totalBidSize > 0 && (totalAskSize / totalBidSize) > 1.2;
           
            return priceMovingDown && volumeIncreasing && favorableImbalance;
        }
       
        private void PlaceEntryOrder(OrderAction action)
        {
            if (!CanModifyOrder())
                return;
               
            try
            {
                double entryPrice;
                string orderName;
               
                if (action == OrderAction.Buy)
                {
                    entryPrice = currentBid; // Start at bid to be competitive
                    orderName = "LongEntry";
                    isLongEntry = true;
                }
                else
                {
                    entryPrice = currentAsk; // Start at ask to be competitive
                    orderName = "ShortEntry";
                    isLongEntry = false;
                }
               
                // Place unmanaged order for immediate chase/escape/execute management
                workingOrder = SubmitOrderUnmanaged(0, action, OrderType.Limit, OrderQuantity, entryPrice, 0, "", orderName);
               
                if (workingOrder != null)
                {
                    currentState = StrategyState.WorkingOrder;
                    entriesAttempted++;
                    Print($"Entry order placed: {orderName} at {entryPrice}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error placing entry order: {ex.Message}");
            }
        }
       
        #endregion
       
        #region Chase Strategy
       
        private void CheckChaseConditions()
        {
            lastChaseUpdate = DateTime.Now;
           
            if (workingOrder == null || workingOrder.OrderState != OrderState.Working)
                return;
               
            double bestPrice = isLongEntry ? currentBid : currentAsk;
            double distanceFromBest = Math.Abs(workingOrder.LimitPrice - bestPrice) / TickSize;
           
            if (distanceFromBest > ChaseMaxDistance)
            {
                if (CanModifyOrder())
                {
                    MoveOrder(workingOrder, bestPrice, "Chase");
                    chaseModifications++;
                }
            }
        }
       
        #endregion
       
        #region Escape Strategy
       
        private bool CheckEscapeConditions()
        {
            if (workingOrder == null || workingOrder.OrderState != OrderState.Working)
                return false;
               
            double oppositePrice = isLongEntry ? currentAsk : currentBid;
            double distanceFromOpposite = Math.Abs(oppositePrice - workingOrder.LimitPrice) / TickSize;
           
            if (distanceFromOpposite <= EscapeDistance)
            {
                // Check market depth imbalance
                double ourSideSize = GetTotalSizeAtLevels(isLongEntry ? MarketDataType.Bid : MarketDataType.Ask, EscapeDepthLevels);
                double oppositeSideSize = GetTotalSizeAtLevels(isLongEntry ? MarketDataType.Ask : MarketDataType.Bid, EscapeDepthLevels);
               
                if (oppositeSideSize > 0)
                {
                    double imbalanceRatio = ourSideSize / oppositeSideSize;
                   
                    if (imbalanceRatio < EscapeImbalanceThreshold)
                    {
                        EscapeOrder(workingOrder, "Escape - Unfavorable Imbalance");
                        escapeModifications++;
                        return true;
                    }
                }
            }
           
            return false;
        }
       
        #endregion
       
        #region Execute Strategy
       
        private bool CheckExecuteConditions()
        {
            if (workingOrder == null || workingOrder.OrderState != OrderState.Working)
                return false;
               
            // Check for favorable imbalance
            double ourSideSize = GetTotalSizeAtLevels(isLongEntry ? MarketDataType.Bid : MarketDataType.Ask, ExecuteDepthLevels);
            double oppositeSideSize = GetTotalSizeAtLevels(isLongEntry ? MarketDataType.Ask : MarketDataType.Bid, ExecuteDepthLevels);
           
            if (oppositeSideSize > 0)
            {
                double imbalanceRatio = ourSideSize / oppositeSideSize;
               
                if (imbalanceRatio > ExecuteImbalanceThreshold)
                {
                    // Move order aggressively
                    double bestPrice = isLongEntry ? currentBid : currentAsk;
                    double newPrice = isLongEntry ?
                        bestPrice + (ExecuteTicksAhead * TickSize) :
                        bestPrice - (ExecuteTicksAhead * TickSize);
                   
                    if (CanModifyOrder())
                    {
                        MoveOrder(workingOrder, newPrice, "Execute - Favorable Imbalance");
                        executeModifications++;
                        return true;
                    }
                }
            }
           
            return false;
        }
       
        #endregion
       
        #region Utility Methods
       
        private double GetTotalSizeAtLevels(MarketDataType side, int levels)
        {
            double totalSize = 0;
            var depth = (side == MarketDataType.Bid) ? bidDepth : askDepth;
            var bestPrice = (side == MarketDataType.Bid) ? currentBid : currentAsk;
           
            var sortedPrices = depth.Keys
                .Where(p => side == MarketDataType.Bid ? p <= bestPrice : p >= bestPrice)
                .OrderBy(p => side == MarketDataType.Bid ? -p : p)
                .Take(levels);
           
            foreach (var price in sortedPrices)
            {
                if (depth.ContainsKey(price))
                    totalSize += depth[price];
            }
           
            return totalSize;
        }
       
        private void MoveOrder(Order order, double newPrice, string reason)
        {
            try
            {
                if (order != null && order.OrderState == OrderState.Working && Math.Abs(order.LimitPrice - newPrice) > double.Epsilon)
                {
                    ChangeOrder(order, order.Quantity, newPrice, 0);
                    RecordModification(order.Name);
                    totalModifications++;
                   
                    Print($"{reason}: Moving {order.Name} from {order.LimitPrice} to {newPrice}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error moving order: {ex.Message}");
            }
        }
       
        private void EscapeOrder(Order order, string reason)
        {
            try
            {
                if (order != null && order.OrderState == OrderState.Working)
                {
                    CancelOrder(order);
                    RecordModification(order.Name);
                    totalModifications++;
                   
                    // Reset to flat state
                    workingOrder = null;
                    currentState = StrategyState.Flat;
                   
                    Print($"{reason}: Cancelling {order.Name}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error escaping order: {ex.Message}");
            }
        }
       
        private bool CanModifyOrder()
        {
            UpdateRiskManagement();
            return modificationsThisMinute < MaxModificationsPerMinute;
        }
       
        private void RecordModification(string orderName)
        {
            orderModifications[orderName + DateTime.Now.Ticks] = DateTime.Now;
            modificationsThisMinute++;
        }
       
        private void UpdateRiskManagement()
        {
            if (DateTime.Now.Subtract(lastMinuteReset).TotalMinutes >= 1)
            {
                modificationsThisMinute = 0;
                lastMinuteReset = DateTime.Now;
            }
        }
       
        private void PrintPerformanceStats()
        {
            Print("=== Advanced Order Management Performance Stats ===");
            Print($"Strategy State: {currentState}");
            Print($"Entries Attempted: {entriesAttempted}");
            Print($"Entries Filled: {entriesFilled}");
            Print($"Fill Rate: {(entriesAttempted > 0 ? (double)entriesFilled / entriesAttempted * 100 : 0):F1}%");
            Print($"Total Modifications: {totalModifications}");
            Print($"Chase Modifications: {chaseModifications}");
            Print($"Escape Modifications: {escapeModifications}");
            Print($"Execute Modifications: {executeModifications}");
        }
       
        #endregion
       
        #region Public Methods
       
        public string GetStrategyStatus()
        {
            var status = new StringBuilder();
            status.AppendLine($"State: {currentState}");
            status.AppendLine($"Working Order: {(workingOrder?.Name ?? "None")}");
            status.AppendLine($"ATM Strategy: {atmStrategyId}");
            status.AppendLine($"Modifications this minute: {modificationsThisMinute}/{MaxModificationsPerMinute}");
            status.AppendLine($"Market: Bid={currentBid}, Ask={currentAsk}");
            return status.ToString();
        }
       
        #endregion
    }
}
